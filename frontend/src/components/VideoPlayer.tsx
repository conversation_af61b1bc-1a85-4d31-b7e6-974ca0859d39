import React, { useRef, useEffect } from 'react';
import videojs from 'video.js';
import 'video.js/dist/video-js.css';

import { Video } from '../types';
import { videoApi } from '../utils/api';

interface VideoPlayerProps {
  video: Video;
  className?: string;
}

const VideoPlayer: React.FC<VideoPlayerProps> = ({ video, className = '' }) => {
  const videoRef = useRef<HTMLDivElement>(null);
  const playerRef = useRef<any>(null);
  const [useHtml5, setUseHtml5] = React.useState(false);

  // Add custom CSS for video player with proper fullscreen support
  useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      /* Normal windowed mode styles */
      .video-js:not(.vjs-fullscreen) {
        width: 100% !important;
        height: auto !important;
        max-height: 60vh !important;
      }

      .video-js:not(.vjs-fullscreen) .vjs-tech {
        width: 100% !important;
        height: auto !important;
        object-fit: contain !important;
      }

      /* Fullscreen mode styles */
      .video-js.vjs-fullscreen {
        width: 100vw !important;
        height: 100vh !important;
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        z-index: 9999 !important;
        background: #000 !important;
      }

      .video-js.vjs-fullscreen .vjs-tech {
        width: 100% !important;
        height: 100% !important;
        object-fit: contain !important;
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
      }

      /* Ensure aspect ratio is preserved in fullscreen */
      .video-js.vjs-fullscreen video {
        object-fit: contain !important;
        width: 100% !important;
        height: 100% !important;
      }

      .vjs-fluid {
        padding-top: 0 !important;
      }

      .html5-video {
        width: 100%;
        height: auto;
        max-width: 100%;
        max-height: 60vh;
      }

      .video-container {
        max-width: 800px;
        margin: 0 auto;
      }

      @media (max-width: 768px) {
        .video-container {
          max-width: 100%;
        }
        .html5-video {
          max-height: 50vh;
        }
      }

      @media (max-width: 480px) {
        .html5-video {
          max-height: 40vh;
        }
      }
    `;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, []);

  useEffect(() => {
    // Make sure Video.js player is only initialized once
    if (!playerRef.current) {
      const videoElement = document.createElement('video-js');
      
      videoElement.classList.add('vjs-big-play-centered');
      videoElement.style.width = '100%';
      videoElement.style.height = '100%';
      videoRef.current?.appendChild(videoElement);

      const player = playerRef.current = videojs(videoElement, {
        autoplay: false,
        controls: true,
        responsive: true,
        fluid: true, // Enable fluid mode for better responsive behavior
        fill: false, // Don't fill the container, maintain aspect ratio
        preload: 'auto',
        playbackRates: [0.5, 1, 1.25, 1.5, 2],
        aspectRatio: video.width && video.height ? `${video.width}:${video.height}` : '16:9',
        sources: [{
          src: videoApi.getVideoUrl(video.filename),
          type: 'video/mp4'
        }],
        poster: video.thumbnail_path
          ? videoApi.getThumbnailUrl(video.thumbnail_path.split('/').pop() || '')
          : undefined,
        html5: {
          vhs: {
            overrideNative: true
          }
        },
        // Fullscreen options
        fullscreen: {
          options: {
            navigationUI: 'hide'
          }
        }
      }, () => {
        console.log('Video.js player initialized');
        // Ensure the player is properly sized
        player.ready(() => {
          console.log('Player ready, triggering resize');
          player.trigger('resize');
          // Try to load the video
          player.load();
        });
      });

      // Handle player errors
      player.on('error', () => {
        const error = player.error();
        console.error('Video player error:', error);
        console.log('Falling back to HTML5 video player');
        setUseHtml5(true);
      });

      // Handle fullscreen events to ensure proper aspect ratio
      player.on('fullscreenchange', () => {
        if (player.isFullscreen()) {
          console.log('Entered fullscreen mode');
          // Force aspect ratio preservation in fullscreen
          const videoEl = player.el().querySelector('video') as HTMLVideoElement;
          const techEl = player.el().querySelector('.vjs-tech') as HTMLElement;

          if (videoEl) {
            videoEl.style.objectFit = 'contain';
            videoEl.style.width = '100%';
            videoEl.style.height = '100%';
            videoEl.style.position = 'absolute';
            videoEl.style.top = '0';
            videoEl.style.left = '0';
          }

          if (techEl) {
            techEl.style.objectFit = 'contain';
            techEl.style.width = '100%';
            techEl.style.height = '100%';
          }

          // Ensure the player container fills the screen
          const playerEl = player.el() as HTMLElement;
          if (playerEl) {
            playerEl.style.width = '100vw';
            playerEl.style.height = '100vh';
            playerEl.style.position = 'fixed';
            playerEl.style.top = '0';
            playerEl.style.left = '0';
            playerEl.style.zIndex = '9999';
            playerEl.style.background = '#000';
          }
        } else {
          console.log('Exited fullscreen mode');
          // Reset styles when exiting fullscreen
          const videoEl = player.el().querySelector('video') as HTMLVideoElement;
          const techEl = player.el().querySelector('.vjs-tech') as HTMLElement;
          const playerEl = player.el() as HTMLElement;

          if (videoEl) {
            videoEl.style.position = '';
            videoEl.style.top = '';
            videoEl.style.left = '';
          }

          if (playerEl) {
            playerEl.style.width = '';
            playerEl.style.height = '';
            playerEl.style.position = '';
            playerEl.style.top = '';
            playerEl.style.left = '';
            playerEl.style.zIndex = '';
            playerEl.style.background = '';
          }

          // Trigger resize to ensure proper sizing when exiting fullscreen
          setTimeout(() => {
            player.trigger('resize');
          }, 100);
        }
      });

      // Handle resize events
      player.on('resize', () => {
        console.log('Player resized');
        const videoEl = player.el().querySelector('video') as HTMLVideoElement;
        if (videoEl && !player.isFullscreen()) {
          videoEl.style.objectFit = 'contain';
        }
      });
    }
  }, [video]);

  // Dispose the Video.js player when the functional component unmounts
  useEffect(() => {
    const player = playerRef.current;

    return () => {
      if (player && !player.isDisposed()) {
        player.dispose();
        playerRef.current = null;
      }
    };
  }, []);

  // Calculate responsive dimensions with better aspect ratio handling
  const getVideoDimensions = () => {
    if (!video.width || !video.height) return { maxWidth: '800px', aspectRatio: '16/9' };

    const aspectRatio = video.width / video.height;
    const isVertical = aspectRatio < 1;
    const isSquare = Math.abs(aspectRatio - 1) < 0.1; // Consider square if ratio is close to 1:1

    if (isVertical) {
      // For vertical videos (like TikTok), limit width more aggressively
      return {
        maxWidth: '400px',
        aspectRatio: `${video.width}/${video.height}`
      };
    } else if (isSquare) {
      // For square videos, use a moderate size
      return {
        maxWidth: '600px',
        aspectRatio: `${video.width}/${video.height}`
      };
    } else {
      // For horizontal videos, allow wider display
      return {
        maxWidth: '800px',
        aspectRatio: `${video.width}/${video.height}`
      };
    }
  };

  const { maxWidth, aspectRatio } = getVideoDimensions();

  return (
    <div className={`bg-white rounded-lg shadow overflow-hidden ${className}`}>
      <div className="video-container" style={{ maxWidth }}>
        <div
          className="relative bg-black rounded-lg overflow-hidden"
          style={{
            aspectRatio,
            width: '100%',
            maxHeight: '60vh'
          }}
        >
          {useHtml5 ? (
            <video
              className="html5-video w-full h-full object-contain"
              controls
              preload="auto"
              poster={video.thumbnail_path
                ? videoApi.getThumbnailUrl(video.thumbnail_path.split('/').pop() || '')
                : undefined}
              style={{
                aspectRatio,
                objectFit: 'contain'
              }}
              onLoadedMetadata={(e) => {
                // Ensure proper aspect ratio is maintained for HTML5 video
                const videoElement = e.target as HTMLVideoElement;
                if (videoElement) {
                  videoElement.style.objectFit = 'contain';
                }
              }}
            >
              <source src={videoApi.getVideoUrl(video.filename)} type="video/mp4" />
              Your browser does not support the video tag.
            </video>
          ) : (
            <div
              ref={videoRef}
              className="w-full h-full"
              style={{
                aspectRatio
              }}
            />
          )}
        </div>
      </div>

      {/* Video metadata below player */}
      <div className="p-4 border-t border-gray-200">
        <h3 className="font-medium text-gray-900">{video.title}</h3>
        <p className="text-sm text-gray-500 mt-1">
          {video.original_filename}
        </p>
        {video.width && video.height && (
          <p className="text-xs text-gray-400 mt-1">
            {video.width} × {video.height}
          </p>
        )}
      </div>
    </div>
  );
};

export default VideoPlayer;
