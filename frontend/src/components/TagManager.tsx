import React, { useState } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import { 
  PlusIcon, 
  XMarkIcon, 
  PencilIcon,
  CheckIcon,
  TagIcon
} from '@heroicons/react/24/outline';

import { Tag, TagCreateData } from '../types';
import { tagApi } from '../utils/api';
import TagColorPicker from './TagColorPicker';

interface TagManagerProps {
  videoId: number;
  tags: Tag[];
}

const TagManager: React.FC<TagManagerProps> = ({ videoId, tags }) => {
  const queryClient = useQueryClient();
  const [showAddForm, setShowAddForm] = useState(false);
  const [newTag, setNewTag] = useState<TagCreateData>({
    name: '',
    color: '#3B82F6',
    description: '',
  });

  // Fetch all available tags for suggestions
  const { data: allTags = [] } = useQuery<Tag[]>({
    queryKey: ['tags'],
    queryFn: () => tagApi.getTags({ skip: 0, limit: 100 }),
  });

  // Create tag mutation
  const createTagMutation = useMutation({
    mutationFn: (data: TagCreateData) => tagApi.createTag(data),
    onSuccess: (createdTag) => {
      // Add the new tag to the video
      addTagMutation.mutate(createdTag.id);
      setShowAddForm(false);
      setNewTag({ name: '', color: '#3B82F6', description: '' });
      // Invalidate all tag related queries
      queryClient.invalidateQueries({ queryKey: ['tags'] });
      queryClient.invalidateQueries({ queryKey: ['tagCloud'] });
    },
    onError: (error: any) => {
      toast.error('Failed to create tag: ' + (error.response?.data?.detail || error.message));
    },
  });

  // Add tag to video mutation
  const addTagMutation = useMutation({
    mutationFn: (tagId: number) => tagApi.addTagToVideo(tagId, videoId),
    onSuccess: () => {
      toast.success('Tag added to video');
      // Invalidate all video and tag related queries with broader patterns
      queryClient.invalidateQueries({ queryKey: ['video', videoId] });
      queryClient.invalidateQueries({ queryKey: ['videos'], exact: false });
      queryClient.invalidateQueries({ queryKey: ['tagCloud'] });
      queryClient.invalidateQueries({ queryKey: ['tags'] });
    },
    onError: (error: any) => {
      toast.error('Failed to add tag: ' + (error.response?.data?.detail || error.message));
    },
  });

  // Remove tag from video mutation
  const removeTagMutation = useMutation({
    mutationFn: (tagId: number) => tagApi.removeTagFromVideo(tagId, videoId),
    onSuccess: () => {
      toast.success('Tag removed from video');
      // Invalidate all video and tag related queries with broader patterns
      queryClient.invalidateQueries({ queryKey: ['video', videoId] });
      queryClient.invalidateQueries({ queryKey: ['videos'], exact: false });
      queryClient.invalidateQueries({ queryKey: ['tagCloud'] });
      queryClient.invalidateQueries({ queryKey: ['tags'] });
    },
    onError: (error: any) => {
      toast.error('Failed to remove tag: ' + (error.response?.data?.detail || error.message));
    },
  });

  const handleCreateTag = () => {
    if (!newTag.name.trim()) {
      toast.error('Tag name is required');
      return;
    }

    createTagMutation.mutate(newTag);
  };

  const handleAddExistingTag = (tagId: number) => {
    addTagMutation.mutate(tagId);
  };

  const handleRemoveTag = (tagId: number) => {
    removeTagMutation.mutate(tagId);
  };

  // Get available tags that aren't already assigned
  const availableTags = allTags.filter(
    tag => !tags.some(videoTag => videoTag.id === tag.id)
  );

  const getRandomColor = () => {
    const colors = [
      '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
      '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
    ];
    return colors[Math.floor(Math.random() * colors.length)];
  };

  return (
    <div className="space-y-4">
      {/* Current Tags */}
      <div className="space-y-2">
        {tags.length === 0 ? (
          <p className="text-sm text-gray-500 italic">No tags assigned</p>
        ) : (
          tags.map((tag) => (
            <div
              key={tag.id}
              className="flex items-center justify-between p-2 rounded-lg border border-gray-200 hover:border-gray-300"
            >
              <div className="flex items-center space-x-2">
                <div
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: tag.color }}
                />
                <div>
                  <span className="text-sm font-medium text-gray-900">{tag.name}</span>
                  {tag.description && (
                    <p className="text-xs text-gray-500">{tag.description}</p>
                  )}
                </div>
              </div>
              <button
                onClick={() => handleRemoveTag(tag.id)}
                disabled={removeTagMutation.isPending}
                className="text-gray-400 hover:text-red-500 disabled:opacity-50"
              >
                <XMarkIcon className="h-4 w-4" />
              </button>
            </div>
          ))
        )}
      </div>

      {/* Add Tag Section */}
      <div className="border-t border-gray-200 pt-4">
        {!showAddForm ? (
          <button
            onClick={() => setShowAddForm(true)}
            className="inline-flex items-center text-sm text-primary-600 hover:text-primary-700 font-medium"
          >
            <PlusIcon className="h-4 w-4 mr-1" />
            Add Tag
          </button>
        ) : (
          <div className="space-y-3">
            {/* Quick Add from Existing Tags */}
            {availableTags.length > 0 && (
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">
                  Quick Add
                </label>
                <div className="flex flex-wrap gap-1">
                  {availableTags.slice(0, 5).map((tag) => (
                    <button
                      key={tag.id}
                      onClick={() => handleAddExistingTag(tag.id)}
                      disabled={addTagMutation.isPending}
                      className="inline-flex items-center px-2 py-1 rounded text-xs font-medium text-white hover:opacity-80 disabled:opacity-50"
                      style={{ backgroundColor: tag.color }}
                    >
                      {tag.name}
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* Create New Tag Form */}
            <div className="border-t border-gray-100 pt-3">
              <label className="block text-xs font-medium text-gray-700 mb-1">
                Create New Tag
              </label>
              <div className="space-y-2">
                <input
                  type="text"
                  value={newTag.name}
                  onChange={(e) => setNewTag(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Tag name"
                  className="block w-full text-sm border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                />
                
                <TagColorPicker
                  color={newTag.color}
                  onChange={(color) => setNewTag(prev => ({ ...prev, color }))}
                />
                
                <textarea
                  value={newTag.description}
                  onChange={(e) => setNewTag(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Description (optional)"
                  rows={2}
                  className="block w-full text-sm border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                />
                
                <div className="flex items-center space-x-2">
                  <button
                    onClick={handleCreateTag}
                    disabled={createTagMutation.isPending || !newTag.name.trim()}
                    className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
                  >
                    <CheckIcon className="h-3 w-3 mr-1" />
                    Create
                  </button>
                  <button
                    onClick={() => {
                      setShowAddForm(false);
                      setNewTag({ name: '', color: '#3B82F6', description: '' });
                    }}
                    className="inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={() => setNewTag(prev => ({ ...prev, color: getRandomColor() }))}
                    className="text-xs text-gray-500 hover:text-gray-700"
                  >
                    Random Color
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default TagManager;
