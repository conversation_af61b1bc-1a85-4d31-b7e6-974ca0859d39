@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --toast-bg: #363636;
  --toast-color: #fff;
}

.dark {
  --toast-bg: #374151;
  --toast-color: #f9fafb;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.dark ::-webkit-scrollbar-track {
  background: #374151;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

.dark ::-webkit-scrollbar-thumb {
  background: #6b7280;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* Video.js custom styles */
.video-js:not(.vjs-fullscreen) {
  width: 100%;
  height: auto;
}

/* Fullscreen video styles */
.video-js.vjs-fullscreen {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 9999 !important;
  background: #000 !important;
}

.video-js.vjs-fullscreen video {
  object-fit: contain !important;
  width: 100% !important;
  height: 100% !important;
}

.video-js .vjs-big-play-button {
  font-size: 2.5em;
  line-height: 2.3;
  height: 2.5em;
  width: 2.5em;
  border-radius: 50%;
  background-color: rgba(43, 51, 63, 0.7);
  border: none;
  margin-top: -1.25em;
  margin-left: -1.25em;
}

.dark .video-js .vjs-big-play-button {
  background-color: rgba(75, 85, 99, 0.8);
}

/* Custom animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.3s ease-in-out;
}

/* Loading spinner */
.spinner {
  border: 2px solid #f3f3f3;
  border-top: 2px solid #3498db;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
