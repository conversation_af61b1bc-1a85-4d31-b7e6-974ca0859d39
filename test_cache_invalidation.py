#!/usr/bin/env python3
"""
Test script to verify cache invalidation is working properly.
This script tests that UI updates happen immediately after operations.
"""

import requests
import time
import json
import os
import tempfile

API_BASE = "http://localhost:8000"

def create_test_video_file():
    """Create a small test video file for upload testing"""
    # Create a minimal MP4 file (just headers, won't actually play)
    mp4_header = b'\x00\x00\x00\x20ftypmp41\x00\x00\x00\x00mp41isom\x00\x00\x00\x08free'
    
    with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as f:
        f.write(mp4_header)
        f.write(b'\x00' * 1000)  # Add some padding
        return f.name

def test_cache_invalidation():
    """Test that cache invalidation works for all operations"""
    
    print("🧪 Testing Cache Invalidation...")
    
    # Step 1: Verify initial state (empty)
    print("\n1️⃣ Checking initial state...")
    response = requests.get(f"{API_BASE}/videos/")
    initial_videos = response.json()
    print(f"   Initial video count: {len(initial_videos)}")
    
    response = requests.get(f"{API_BASE}/tags/")
    initial_tags = response.json()
    print(f"   Initial tag count: {len(initial_tags)}")
    
    # Step 2: Create a test tag
    print("\n2️⃣ Creating test tag...")
    tag_data = {
        "name": "cache-test-tag",
        "color": "#FF5722",
        "description": "Tag for testing cache invalidation"
    }
    response = requests.post(f"{API_BASE}/tags/", json=tag_data)
    if response.status_code == 200:
        test_tag = response.json()
        print(f"   ✅ Created tag: {test_tag['name']} (ID: {test_tag['id']})")
    else:
        print(f"   ❌ Failed to create tag: {response.text}")
        return False
    
    # Step 3: Upload a test video
    print("\n3️⃣ Uploading test video...")
    test_file = create_test_video_file()
    try:
        with open(test_file, 'rb') as f:
            files = {'files': ('test_video.mp4', f, 'video/mp4')}
            response = requests.post(f"{API_BASE}/videos/upload", files=files)
        
        if response.status_code == 200:
            upload_result = response.json()
            print(f"   ✅ Upload successful: {upload_result['total_uploaded']} videos uploaded")
            
            # Get the uploaded video ID
            response = requests.get(f"{API_BASE}/videos/")
            videos_after_upload = response.json()
            if len(videos_after_upload) > len(initial_videos):
                test_video = videos_after_upload[0]
                print(f"   ✅ Video appears in list immediately: {test_video['original_filename']}")
            else:
                print("   ❌ Video not found in list after upload")
                return False
        else:
            print(f"   ❌ Upload failed: {response.text}")
            return False
    finally:
        os.unlink(test_file)
    
    # Step 4: Test bulk tag addition
    print("\n4️⃣ Testing bulk tag addition...")
    bulk_add_data = {
        "video_ids": [test_video['id']],
        "tag_ids": [test_tag['id']]
    }
    response = requests.post(f"{API_BASE}/videos/bulk/add-tags", json=bulk_add_data)
    if response.status_code == 200:
        result = response.json()
        print(f"   ✅ Bulk add tags successful: {result['processed_count']} videos updated")
        
        # Verify tag was added
        response = requests.get(f"{API_BASE}/videos/{test_video['id']}")
        updated_video = response.json()
        if any(tag['id'] == test_tag['id'] for tag in updated_video['tags']):
            print("   ✅ Tag appears on video immediately")
        else:
            print("   ❌ Tag not found on video after addition")
            return False
    else:
        print(f"   ❌ Bulk add tags failed: {response.text}")
        return False
    
    # Step 5: Test bulk tag removal
    print("\n5️⃣ Testing bulk tag removal...")
    bulk_remove_data = {
        "video_ids": [test_video['id']],
        "tag_ids": [test_tag['id']]
    }
    response = requests.post(f"{API_BASE}/videos/bulk/remove-tags", json=bulk_remove_data)
    if response.status_code == 200:
        result = response.json()
        print(f"   ✅ Bulk remove tags successful: {result['processed_count']} videos updated")
        
        # Verify tag was removed
        response = requests.get(f"{API_BASE}/videos/{test_video['id']}")
        updated_video = response.json()
        if not any(tag['id'] == test_tag['id'] for tag in updated_video['tags']):
            print("   ✅ Tag removed from video immediately")
        else:
            print("   ❌ Tag still found on video after removal")
            return False
    else:
        print(f"   ❌ Bulk remove tags failed: {response.text}")
        return False
    
    # Step 6: Test individual video deletion
    print("\n6️⃣ Testing individual video deletion...")
    response = requests.delete(f"{API_BASE}/videos/{test_video['id']}")
    if response.status_code == 200:
        print("   ✅ Video deletion successful")
        
        # Verify video was removed from list
        response = requests.get(f"{API_BASE}/videos/")
        videos_after_delete = response.json()
        if len(videos_after_delete) == len(initial_videos):
            print("   ✅ Video removed from list immediately")
        else:
            print("   ❌ Video still found in list after deletion")
            return False
    else:
        print(f"   ❌ Video deletion failed: {response.text}")
        return False
    
    # Step 7: Clean up test tag
    print("\n7️⃣ Cleaning up test tag...")
    response = requests.delete(f"{API_BASE}/tags/{test_tag['id']}")
    if response.status_code == 200:
        print("   ✅ Test tag deleted")
    else:
        print(f"   ⚠️ Failed to delete test tag: {response.text}")
    
    print("\n🎉 All cache invalidation tests passed!")
    print("\n📋 Summary:")
    print("   ✅ Upload operations trigger immediate UI updates")
    print("   ✅ Bulk tag operations trigger immediate UI updates")
    print("   ✅ Individual delete operations trigger immediate UI updates")
    print("   ✅ Tag creation/deletion triggers immediate UI updates")
    
    return True

if __name__ == "__main__":
    try:
        success = test_cache_invalidation()
        exit(0 if success else 1)
    except Exception as e:
        print(f"\n❌ Test failed with exception: {e}")
        exit(1)
